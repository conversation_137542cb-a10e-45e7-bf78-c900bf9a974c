using Microsoft.AspNetCore.Mvc;
using Orders.Models;

namespace Orders.Controllers;

[ApiController]
[Route("api/[controller]")]
public class OrdersController : ControllerBase
{
    private static readonly List<Order> _orders = new()
    {
        new Order 
        { 
            Id = 1, 
            UserId = 1, 
            Items = new List<OrderItem>
            {
                new OrderItem { ProductId = 1, ProductName = "Laptop", Quantity = 1, UnitPrice = 999.99m }
            },
            TotalAmount = 999.99m,
            Status = OrderStatus.Delivered,
            ShippingAddress = "123 Main St, City, State 12345"
        },
        new Order 
        { 
            Id = 2, 
            UserId = 2, 
            Items = new List<OrderItem>
            {
                new OrderItem { ProductId = 2, ProductName = "Smartphone", Quantity = 1, UnitPrice = 699.99m },
                new OrderItem { ProductId = 3, ProductName = "Coffee Mug", Quantity = 2, UnitPrice = 12.99m }
            },
            TotalAmount = 725.97m,
            Status = OrderStatus.Processing,
            ShippingAddress = "456 Oak Ave, City, State 67890"
        }
    };

    [HttpGet]
    public ActionResult<IEnumerable<Order>> GetOrders([FromQuery] int? userId = null)
    {
        var orders = _orders.AsEnumerable();
        
        if (userId.HasValue)
        {
            orders = orders.Where(o => o.UserId == userId.Value);
        }
        
        return Ok(orders);
    }

    [HttpGet("{id}")]
    public ActionResult<Order> GetOrder(int id)
    {
        var order = _orders.FirstOrDefault(o => o.Id == id);
        if (order == null)
        {
            return NotFound();
        }
        return Ok(order);
    }

    [HttpPost]
    public ActionResult<Order> CreateOrder(CreateOrderRequest request)
    {
        var order = new Order
        {
            Id = _orders.Count > 0 ? _orders.Max(o => o.Id) + 1 : 1,
            UserId = request.UserId,
            Items = request.Items.Select(item => new OrderItem
            {
                ProductId = item.ProductId,
                ProductName = item.ProductName,
                Quantity = item.Quantity,
                UnitPrice = item.UnitPrice
            }).ToList(),
            ShippingAddress = request.ShippingAddress
        };

        order.TotalAmount = order.Items.Sum(item => item.TotalPrice);
        _orders.Add(order);
        
        return CreatedAtAction(nameof(GetOrder), new { id = order.Id }, order);
    }

    [HttpPatch("{id}/status")]
    public ActionResult<Order> UpdateOrderStatus(int id, UpdateOrderStatusRequest request)
    {
        var order = _orders.FirstOrDefault(o => o.Id == id);
        if (order == null)
        {
            return NotFound();
        }

        order.Status = request.Status;
        order.UpdatedAt = DateTime.UtcNow;

        return Ok(order);
    }

    [HttpDelete("{id}")]
    public ActionResult CancelOrder(int id)
    {
        var order = _orders.FirstOrDefault(o => o.Id == id);
        if (order == null)
        {
            return NotFound();
        }

        if (order.Status == OrderStatus.Shipped || order.Status == OrderStatus.Delivered)
        {
            return BadRequest("Cannot cancel shipped or delivered orders");
        }

        order.Status = OrderStatus.Cancelled;
        order.UpdatedAt = DateTime.UtcNow;
        
        return NoContent();
    }
}
