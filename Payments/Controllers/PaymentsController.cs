using Microsoft.AspNetCore.Mvc;
using Payments.Models;

namespace Payments.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PaymentsController : ControllerBase
{
    private static readonly List<Payment> _payments = new()
    {
        new Payment 
        { 
            Id = 1, 
            OrderId = 1, 
            UserId = 1, 
            Amount = 999.99m, 
            Method = PaymentMethod.CreditCard,
            Status = PaymentStatus.Completed,
            TransactionId = "TXN_001",
            PaymentGateway = "Stripe"
        },
        new Payment 
        { 
            Id = 2, 
            OrderId = 2, 
            UserId = 2, 
            Amount = 725.97m, 
            Method = PaymentMethod.PayPal,
            Status = PaymentStatus.Processing,
            TransactionId = "TXN_002",
            PaymentGateway = "PayPal"
        }
    };

    [HttpGet]
    public ActionResult<IEnumerable<Payment>> GetPayments([FromQuery] int? userId = null, [FromQuery] int? orderId = null)
    {
        var payments = _payments.AsEnumerable();
        
        if (userId.HasValue)
        {
            payments = payments.Where(p => p.UserId == userId.Value);
        }
        
        if (orderId.HasValue)
        {
            payments = payments.Where(p => p.OrderId == orderId.Value);
        }
        
        return Ok(payments);
    }

    [HttpGet("{id}")]
    public ActionResult<Payment> GetPayment(int id)
    {
        var payment = _payments.FirstOrDefault(p => p.Id == id);
        if (payment == null)
        {
            return NotFound();
        }
        return Ok(payment);
    }

    [HttpPost("process")]
    public ActionResult<Payment> ProcessPayment(ProcessPaymentRequest request)
    {
        // Simulate payment processing
        var isSuccessful = Random.Shared.NextDouble() > 0.1; // 90% success rate
        
        var payment = new Payment
        {
            Id = _payments.Count > 0 ? _payments.Max(p => p.Id) + 1 : 1,
            OrderId = request.OrderId,
            UserId = request.UserId,
            Amount = request.Amount,
            Method = request.Method,
            Status = isSuccessful ? PaymentStatus.Completed : PaymentStatus.Failed,
            TransactionId = $"TXN_{Guid.NewGuid().ToString()[..8].ToUpper()}",
            PaymentGateway = GetPaymentGateway(request.Method),
            FailureReason = isSuccessful ? null : "Insufficient funds"
        };

        _payments.Add(payment);
        
        return CreatedAtAction(nameof(GetPayment), new { id = payment.Id }, payment);
    }

    [HttpPost("{id}/refund")]
    public ActionResult<Payment> RefundPayment(int id, RefundRequest request)
    {
        var payment = _payments.FirstOrDefault(p => p.Id == id);
        if (payment == null)
        {
            return NotFound();
        }

        if (payment.Status != PaymentStatus.Completed)
        {
            return BadRequest("Can only refund completed payments");
        }

        if (request.Amount > payment.Amount)
        {
            return BadRequest("Refund amount cannot exceed payment amount");
        }

        payment.Status = PaymentStatus.Refunded;
        payment.UpdatedAt = DateTime.UtcNow;

        return Ok(payment);
    }

    private static string GetPaymentGateway(PaymentMethod method)
    {
        return method switch
        {
            PaymentMethod.CreditCard or PaymentMethod.DebitCard => "Stripe",
            PaymentMethod.PayPal => "PayPal",
            PaymentMethod.BankTransfer => "Plaid",
            PaymentMethod.DigitalWallet => "Apple Pay",
            _ => "Unknown"
        };
    }
}
