using Ocelot.DependencyInjection;
using Ocelot.Middleware;

var builder = WebApplication.CreateBuilder(args);

// Add Ocelot configuration
builder.Configuration.AddJsonFile("ocelot.json", optional: false, reloadOnChange: true);

// Add services to the container.
builder.Services.AddOcelot();

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseHttpsRedirection();

// Use Ocelot middleware
await app.UseOcelot();

app.Run();
