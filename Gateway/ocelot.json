{"Routes": [{"DownstreamPathTemplate": "/api/users/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5001}], "UpstreamPathTemplate": "/api/users/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, {"DownstreamPathTemplate": "/api/products/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5002}], "UpstreamPathTemplate": "/api/products/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, {"DownstreamPathTemplate": "/api/orders/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5003}], "UpstreamPathTemplate": "/api/orders/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"]}, {"DownstreamPathTemplate": "/api/payments/{everything}", "DownstreamScheme": "http", "DownstreamHostAndPorts": [{"Host": "localhost", "Port": 5004}], "UpstreamPathTemplate": "/api/payments/{everything}", "UpstreamHttpMethod": ["GET", "POST", "PUT", "DELETE", "PATCH"]}], "GlobalConfiguration": {"BaseUrl": "http://localhost:5117"}}