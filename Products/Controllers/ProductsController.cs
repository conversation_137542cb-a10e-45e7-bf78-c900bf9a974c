using Microsoft.AspNetCore.Mvc;
using Products.Models;

namespace Products.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private static readonly List<Product> _products = new()
    {
        new Product { Id = 1, Name = "Laptop", Description = "High-performance laptop", Price = 999.99m, Category = "Electronics", StockQuantity = 50 },
        new Product { Id = 2, Name = "Smartphone", Description = "Latest smartphone", Price = 699.99m, Category = "Electronics", StockQuantity = 100 },
        new Product { Id = 3, Name = "Coffee Mug", Description = "Ceramic coffee mug", Price = 12.99m, Category = "Home", StockQuantity = 200 }
    };

    [HttpGet]
    public ActionResult<IEnumerable<Product>> GetProducts([FromQuery] string? category = null)
    {
        var products = _products.Where(p => p.IsActive);
        
        if (!string.IsNullOrEmpty(category))
        {
            products = products.Where(p => p.Category.Equals(category, StringComparison.OrdinalIgnoreCase));
        }
        
        return Ok(products);
    }

    [HttpGet("{id}")]
    public ActionResult<Product> GetProduct(int id)
    {
        var product = _products.FirstOrDefault(p => p.Id == id && p.IsActive);
        if (product == null)
        {
            return NotFound();
        }
        return Ok(product);
    }

    [HttpPost]
    public ActionResult<Product> CreateProduct(CreateProductRequest request)
    {
        var product = new Product
        {
            Id = _products.Count > 0 ? _products.Max(p => p.Id) + 1 : 1,
            Name = request.Name,
            Description = request.Description,
            Price = request.Price,
            Category = request.Category,
            StockQuantity = request.StockQuantity,
            ImageUrl = request.ImageUrl
        };

        _products.Add(product);
        return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
    }

    [HttpPut("{id}")]
    public ActionResult<Product> UpdateProduct(int id, UpdateProductRequest request)
    {
        var product = _products.FirstOrDefault(p => p.Id == id && p.IsActive);
        if (product == null)
        {
            return NotFound();
        }

        product.Name = request.Name;
        product.Description = request.Description;
        product.Price = request.Price;
        product.Category = request.Category;
        product.StockQuantity = request.StockQuantity;
        product.ImageUrl = request.ImageUrl;
        product.UpdatedAt = DateTime.UtcNow;

        return Ok(product);
    }

    [HttpPatch("{id}/stock")]
    public ActionResult<Product> UpdateStock(int id, UpdateStockRequest request)
    {
        var product = _products.FirstOrDefault(p => p.Id == id && p.IsActive);
        if (product == null)
        {
            return NotFound();
        }

        product.StockQuantity = request.Quantity;
        product.UpdatedAt = DateTime.UtcNow;

        return Ok(product);
    }

    [HttpDelete("{id}")]
    public ActionResult DeleteProduct(int id)
    {
        var product = _products.FirstOrDefault(p => p.Id == id && p.IsActive);
        if (product == null)
        {
            return NotFound();
        }

        product.IsActive = false;
        product.UpdatedAt = DateTime.UtcNow;
        return NoContent();
    }
}
